# Combined Pipeline API Documentation

## Overview

The Combined Pipeline API endpoint (`/combined-pipeline/`) integrates Kokoro TTS, style transfer, and timbre transfer in a sequential pipeline, providing a comprehensive audio processing solution.

## Endpoint

**POST** `/combined-pipeline/`

## Processing Flow

1. **Kokoro TTS Generation**: Convert input text to speech using Kokoro TTS with 6-second audio chunks
2. **Style Transfer** (Optional): Apply style transfer if `style_transfer_audio` is provided
3. **Timbre Transfer**: Apply timbre transfer using the user's reference audio
4. **Audio Processing**: Apply optional noise cancellation and normalization

## Input Parameters

### Required Parameters

- **`text`** (string): Text to be converted to speech using Kokoro TTS
- **`voice`** (string): Voice model for Kokoro TTS generation
  - Options: `"am_michael"`, `"af_nicole"`, `"bm_george"`, `"bf_emma"`
- **`user_audio`** (file): Reference audio file for timbre transfer

### Optional Parameters

- **`style_transfer_audio`** (file, optional): Reference audio file for style transfer processing
  - If not provided, style transfer step is skipped
- **`noise_cancellation`** (boolean, default: false): Apply noise cancellation to reference audio
- **`normalization`** (boolean, default: false): Apply audio normalization as preprocessing

## Response

- **Content-Type**: `audio/wav`
- **Headers**: 
  - `X-Inference-Latency`: Total processing time in seconds
- **Body**: Final processed audio file

## Example Usage

### Python with requests

```python
import requests

# Basic usage (no style transfer)
with open("user_reference.wav", "rb") as user_audio:
    response = requests.post(
        "http://localhost:8000/combined-pipeline/",
        data={
            "text": "Hello, this is a test of the combined pipeline.",
            "voice": "am_michael",
            "noise_cancellation": False,
            "normalization": False,
        },
        files={
            "user_audio": user_audio,
        }
    )

# Save the result
with open("output.wav", "wb") as f:
    f.write(response.content)
```

### Full pipeline with style transfer

```python
import requests

with open("style_reference.wav", "rb") as style_audio, \
     open("user_reference.wav", "rb") as user_audio:
    
    response = requests.post(
        "http://localhost:8000/combined-pipeline/",
        data={
            "text": "This example includes style transfer processing.",
            "voice": "af_nicole",
            "noise_cancellation": True,
            "normalization": True,
        },
        files={
            "style_transfer_audio": style_audio,
            "user_audio": user_audio,
        }
    )

# Save the result
with open("output_full_pipeline.wav", "wb") as f:
    f.write(response.content)
```

### cURL Example

```bash
# Basic usage
curl -X POST "http://localhost:8000/combined-pipeline/" \
  -F "text=Hello, this is a test message." \
  -F "voice=am_michael" \
  -F "user_audio=@user_reference.wav" \
  -F "noise_cancellation=false" \
  -F "normalization=false" \
  -o output.wav

# Full pipeline with all options
curl -X POST "http://localhost:8000/combined-pipeline/" \
  -F "text=This is a full pipeline test." \
  -F "voice=af_nicole" \
  -F "style_transfer_audio=@style_reference.wav" \
  -F "user_audio=@user_reference.wav" \
  -F "noise_cancellation=true" \
  -F "normalization=true" \
  -o output_full.wav
```

## Voice Options

- **`am_michael`**: American English Male
- **`af_nicole`**: American English Female  
- **`bm_george`**: British English Male
- **`bf_emma`**: British English Female

## Audio Processing Options

### Noise Cancellation
- Applied to the reference audio (`user_audio`) before timbre transfer
- Uses DeepFilterNet for noise reduction
- Recommended for noisy reference audio

### Normalization
- Applied to both source and reference audio before timbre transfer
- Normalizes audio levels for consistent processing
- Recommended for audio with varying volume levels

## Error Handling

The endpoint returns appropriate HTTP status codes:

- **200**: Success - returns processed audio
- **400**: Bad Request - invalid parameters (e.g., empty text)
- **500**: Internal Server Error - processing failed

Error responses include a JSON body with error details:

```json
{
  "detail": "Error description"
}
```

## Performance Notes

- Processing time varies based on text length and enabled options
- Typical processing time: 10-60 seconds depending on text length
- GPU acceleration is used when available
- Memory usage is optimized through pipeline management

## Limitations

- Maximum recommended text length: ~500 characters for optimal performance
- Supported audio formats: WAV, MP3, FLAC (automatically converted to WAV)
- Reference audio should be at least 2-3 seconds for best results
- Processing is sequential - longer texts will take proportionally longer

## Testing

Use the provided test script to verify the endpoint:

```bash
python3 test_combined_pipeline.py
```

This will run several test cases and save output files for verification.
