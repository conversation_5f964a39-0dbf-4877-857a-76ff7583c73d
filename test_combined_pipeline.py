#!/usr/bin/env python3
"""
Test script for the combined pipeline endpoint.
This script demonstrates how to use the new /combined-pipeline/ endpoint.
"""

import requests
import os
import tempfile
import soundfile as sf
import numpy as np

# Configuration
SERVER_URL = "http://localhost:8008"
COMBINED_PIPELINE_ENDPOINT = f"{SERVER_URL}/combined-pipeline/"

def create_test_audio(duration_sec=3, sample_rate=24000, frequency=440):
    """Create a simple test audio file (sine wave)"""
    t = np.linspace(0, duration_sec, int(sample_rate * duration_sec), False)
    audio = np.sin(2 * np.pi * frequency * t) * 0.5
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
        sf.write(temp_file.name, audio, sample_rate)
        return temp_file.name

def test_combined_pipeline():
    """Test the combined pipeline endpoint"""
    print("🧪 Testing Combined Pipeline Endpoint")
    print("=" * 50)
    
    # Create test audio files
    print("📁 Creating test audio files...")
    style_audio_path = create_test_audio(duration_sec=2, frequency=440)  # A4 note
    user_audio_path = create_test_audio(duration_sec=3, frequency=880)   # A5 note
    
    try:
        # Test parameters
        test_cases = [
            {
                "name": "Basic Pipeline (no style transfer) - Short Text",
                "data": {
                    "text": "Hello, this is a test of the combined pipeline.",
                    "voice": "am_michael",
                    "noise_cancellation": False,
                    "normalization": False,
                },
                "files": {
                    "user_audio": open(user_audio_path, "rb"),
                }
            },
            {
                "name": "Chunked Processing Test - Long Text",
                "data": {
                    "text": """Hello, this is a comprehensive test of the combined audio processing pipeline with chunked processing.
                    We are testing the integration of Kokoro text-to-speech, style transfer, and timbre transfer technologies.

                    This pipeline should be able to handle much longer text inputs by processing them in chunks.
                    Each chunk goes through the complete pipeline: first TTS generation, then style transfer if provided, and finally timbre transfer.

                    The system should maintain audio quality while preventing memory overflow issues.
                    This is particularly important for longer audio sequences that might otherwise cause CUDA out of memory errors.

                    Let's see how well this chunked processing approach works in practice.
                    The chunked approach processes each 6-second segment individually through style transfer and timbre transfer.
                    This should allow us to process much longer text without running into GPU memory limitations.

                    We can now handle text that would generate several minutes of audio without any memory issues.
                    Each chunk is processed sequentially, which is more memory-efficient than processing the entire audio at once.
                    The final result should be a seamless concatenation of all processed chunks.""",
                    "voice": "am_michael",
                    "noise_cancellation": False,
                    "normalization": False,
                },
                "files": {
                    "user_audio": open(user_audio_path, "rb"),
                }
            },
            {
                "name": "Full Pipeline (with style transfer)",
                "data": {
                    "text": "This test includes style transfer processing with chunked approach.",
                    "voice": "af_nicole",
                    "noise_cancellation": True,
                    "normalization": True,
                },
                "files": {
                    "style_transfer_audio": open(style_audio_path, "rb"),
                    "user_audio": open(user_audio_path, "rb"),
                }
            },
            {
                "name": "Pipeline with Normalization Only",
                "data": {
                    "text": "Testing normalization without noise cancellation.",
                    "voice": "bm_george",
                    "noise_cancellation": False,
                    "normalization": True,
                },
                "files": {
                    "user_audio": open(user_audio_path, "rb"),
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔬 Test Case {i}: {test_case['name']}")
            print("-" * 40)
            
            try:
                # Make request
                print("📤 Sending request to combined pipeline...")
                response = requests.post(
                    COMBINED_PIPELINE_ENDPOINT,
                    data=test_case["data"],
                    files=test_case["files"],
                    timeout=300  # 5 minutes timeout
                )
                
                # Check response
                if response.status_code == 200:
                    print("✅ Request successful!")
                    
                    # Get latency from headers
                    latency = response.headers.get("X-Inference-Latency", "Unknown")
                    print(f"⏱️ Inference latency: {latency} seconds")
                    
                    # Save output
                    output_filename = f"combined_pipeline_output_test_{i}.wav"
                    with open(output_filename, "wb") as f:
                        f.write(response.content)
                    print(f"💾 Output saved: {output_filename}")
                    
                    # Check file size
                    file_size = os.path.getsize(output_filename)
                    print(f"📊 Output file size: {file_size} bytes")
                    
                else:
                    print(f"❌ Request failed with status code: {response.status_code}")
                    print(f"📄 Response: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ Request error: {e}")
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
            
            finally:
                # Close file handles
                for file_handle in test_case["files"].values():
                    if hasattr(file_handle, 'close'):
                        file_handle.close()
    
    finally:
        # Clean up test files
        print("\n🧹 Cleaning up test files...")
        try:
            os.remove(style_audio_path)
            os.remove(user_audio_path)
            print("✅ Test files cleaned up")
        except OSError as e:
            print(f"⚠️ Could not clean up test files: {e}")

def test_endpoint_availability():
    """Test if the server and endpoint are available"""
    print("🔍 Checking server availability...")
    
    try:
        # Check if server is running
        response = requests.get(f"{SERVER_URL}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"❌ Server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure the FastAPI server is running on localhost:8000")
        return False

if __name__ == "__main__":
    print("🚀 Combined Pipeline Test Suite")
    print("=" * 50)
    
    # Check server availability first
    if test_endpoint_availability():
        print("\n" + "=" * 50)
        test_combined_pipeline()
    else:
        print("\n❌ Cannot proceed with tests - server is not available")
        print("💡 Start the server with: uvicorn fastapi_server:app --host 0.0.0.0 --port 8000")
    
    print("\n🏁 Test suite completed")
